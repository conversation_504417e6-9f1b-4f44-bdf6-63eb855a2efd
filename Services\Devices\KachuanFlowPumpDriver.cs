using System;
using System.Threading.Tasks;
using PEMTestSystem.Models.Devices;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// 卡川 DI Pump550 流量泵驱动
    /// 支持 Modbus RTU 通信协议，提供完整的流量控制和监测功能
    /// </summary>
    /// <remarks>
    /// 该驱动程序适用于卡川DI Pump550系列流量泵，通过Modbus RTU协议实现设备控制
    /// 主要功能包括：
    /// - 流量设置和监测（100-400000 mL/min，精度10）
    /// - 泵启停控制
    /// - 方向控制（正向/反向）
    /// - 实时状态监控
    /// - 参数保存和恢复
    /// - 485通信控制
    ///
    /// 注意：该泵只能设定转速，流量通过转换系数计算得出
    /// 转速存储格式：整型*100（例如：1000 RPM 存储为 100000）
    ///
    /// ==================== 方法目录索引 ====================
    ///
    /// 【构造函数】
    /// - KachuanFlowPumpDriver(string, int, byte, string, string) // public - 构造函数，初始化流量泵驱动
    ///
    /// 【属性】
    /// - DeviceId, Type, DeviceName, Model                    // public    - 设备基本信息属性
    /// - LastCommunicationTime, Status, IsConnected          // public    - 设备状态属性
    /// - CurrentFlowRate, TargetFlowRate, IsRunning           // public    - 流量泵状态属性
    ///
    /// 【事件】
    /// - StatusChanged                                        // public    - 设备状态变化事件
    /// - FlowRateChanged                                      // public    - 流量变化事件
    /// - OnStatusChanged(DeviceStatus, DeviceStatus, string?) // private   - 触发状态变化事件
    /// - OnFlowRateChanged(double, double)                    // private   - 触发流量变化事件
    ///
    /// 【IDevice接口实现】
    /// - ConnectAsync()                                       // public    - 连接到流量泵设备
    /// - DisconnectAsync()                                    // public    - 断开流量泵连接
    /// - InitializeAsync()                                    // public    - 初始化流量泵
    /// - ResetAsync()                                         // public    - 重置流量泵
    /// - GetDeviceInfoAsync()                                 // public    - 获取设备详细信息
    ///
    /// 【IFlowPump接口实现】
    /// - SetFlowRateAsync(double)                             // public    - 设置目标流量
    /// - GetFlowRateAsync()                                   // public    - 获取当前实际流量
    /// - StartAsync()                                         // public    - 启动流量泵
    /// - StopAsync()                                          // public    - 停止流量泵
    /// - GetRunningStatusAsync()                              // public    - 获取运行状态
    /// - SetDirectionAsync(bool)                              // public    - 设置泵方向
    ///
    /// 【私有辅助方法】
    /// - RefreshStatusAsync()                                 // private   - 刷新设备状态
    /// - ValidateFlowRate(double)                             // private   - 验证流量范围
    /// - ConvertFlowRateToRpm(double)                         // private   - 流量转换为转速
    /// - ConvertRpmToFlowRate(double)                         // private   - 转速转换为流量
    /// - ConvertRpmToIntegerRegisters(double)                 // private   - 转速转换为整型寄存器值
    /// - ConvertIntegerRegistersToRpm(ushort, ushort)        // private   - 整型寄存器值转换为转速
    ///
    /// 【资源管理】
    /// - Dispose(bool)                                        // protected - 释放资源的具体实现
    ///
    /// ====================================================
    /// </remarks>
    public class KachuanFlowPumpDriver : ModbusRtuBase, IFlowPump
    {
        // 线圈地址定义
        private const ushort START_STOP_COIL = 0x1001;      // 起停控制
        private const ushort DRAIN_COIL = 0x1002;           // 排空控制
        private const ushort DIRECTION_COIL = 0x1003;       // 方向控制
        private const ushort RS485_ENABLE_COIL = 0x1004;    // 485控制使能
        private const ushort SAVE_PARAMS_COIL = 0x100A;     // 保存参数

        // 保持寄存器地址定义
        private const ushort SPEED_HIGH_REGISTER = 0x3009;  // 转速高16位（整型*100）
        private const ushort SPEED_LOW_REGISTER = 0x300A;   // 转速低16位（整型*100）
        private const ushort REAL_SPEED_HIGH_REGISTER = 0x300B; // 实时转速高16位（整型*100）
        private const ushort REAL_SPEED_LOW_REGISTER = 0x300C;  // 实时转速低16位（整型*100）
        private const ushort REVERSE_TIME_REGISTER = 0x3007;    // 反转时长
        private const ushort PEDAL_MODE_REGISTER = 0x3008;      // 脚踏开关模式

        private const double MIN_FLOW_RATE = 100.0;         // 最小流量 mL/min
        private const double MAX_FLOW_RATE = 400000.0;      // 最大流量 mL/min
        private const double FLOW_ACCURACY = 10.0;          // 流量精度 mL/min

        // 流量与转速的转换系数（需要根据实际泵的规格调整）
        private const double FLOW_TO_RPM_RATIO = 0.01;      // 1 mL/min = 0.01 RPM（示例值）

        private double _currentFlowRate = 0.0;
        private double _targetFlowRate = 0.0;
        private bool _isRunning = false;
        private bool _isReverse = false;

        public KachuanFlowPumpDriver(
            string portName,
            int baudRate,
            byte slaveAddress,
            string deviceId,
            string deviceName)
            : base(portName, baudRate, slaveAddress)
        {
            DeviceId = deviceId;
            Type = slaveAddress == 1 ? DeviceType.Pump1 : DeviceType.Pump2;
            DeviceName = deviceName;
            Model = "卡川DI Pump550";

            App.AlarmService.Debug("流量泵驱动", $"初始化卡川流量泵驱动 - 设备ID: {deviceId}, 从站地址: {slaveAddress}");
        }

        #region IDevice 属性实现

        public string DeviceId { get; }
        public DeviceType Type { get; }
        public string DeviceName { get; }
        public string Model { get; }
        public DateTime LastCommunicationTime { get; private set; }

        private DeviceStatus _status = DeviceStatus.Disconnected;
        public DeviceStatus Status
        {
            get => _status;
            private set
            {
                if (_status != value)
                {
                    var oldStatus = _status;
                    _status = value;
                    OnStatusChanged(oldStatus, value);
                }
            }
        }

        public bool IsConnected => Status == DeviceStatus.Connected || Status == DeviceStatus.Running;

        #endregion

        #region IFlowPump 属性实现

        public double CurrentFlowRate => _currentFlowRate;
        public double TargetFlowRate => _targetFlowRate;
        public bool IsRunning => _isRunning;

        #endregion

        #region 事件

        public event EventHandler<DeviceStatusChangedEventArgs>? StatusChanged;
        public event EventHandler<FlowRateChangedEventArgs>? FlowRateChanged;

        private void OnStatusChanged(DeviceStatus oldStatus, DeviceStatus newStatus, string? message = null)
        {
            var args = new DeviceStatusChangedEventArgs(DeviceId, oldStatus, newStatus, message);
            StatusChanged?.Invoke(this, args);
            
            App.AlarmService.Info("设备状态", $"流量泵 {DeviceName} 状态变更: {oldStatus} -> {newStatus}");
        }

        private void OnFlowRateChanged(double currentFlow, double targetFlow)
        {
            var args = new FlowRateChangedEventArgs(DeviceId, currentFlow, targetFlow);
            FlowRateChanged?.Invoke(this, args);
        }

        #endregion

        #region IDevice 方法实现

        public override async Task<bool> ConnectAsync()
        {
            try
            {
                Status = DeviceStatus.Connecting;
                App.AlarmService.Info("设备连接", $"正在连接流量泵 {DeviceName}");

                var connected = await base.ConnectAsync();
                if (!connected)
                {
                    Status = DeviceStatus.Error;
                    return false;
                }

                // 启用 485 控制
                var enableResult = await WriteSingleCoilAsync(RS485_ENABLE_COIL, true);
                if (!enableResult)
                {
                    Status = DeviceStatus.Error;
                    App.AlarmService.Error("设备连接", $"流量泵 {DeviceName} 启用485控制失败");
                    return false;
                }

                // 测试通信 - 读取实时转速
                var testResult = await ReadHoldingRegistersAsync(REAL_SPEED_HIGH_REGISTER, 2);
                if (testResult == null)
                {
                    Status = DeviceStatus.Error;
                    App.AlarmService.Error("设备连接", $"流量泵 {DeviceName} 通信测试失败");
                    return false;
                }

                Status = DeviceStatus.Connected;
                LastCommunicationTime = DateTime.Now;
                App.AlarmService.Info("设备连接", $"流量泵 {DeviceName} 连接成功");

                // 读取初始状态
                await RefreshStatusAsync();

                return true;
            }
            catch (Exception ex)
            {
                Status = DeviceStatus.Error;
                App.AlarmService.Error("设备连接", $"流量泵 {DeviceName} 连接异常", ex);
                return false;
            }
        }

        public override async Task<bool> DisconnectAsync()
        {
            try
            {
                // 停止泵
                await StopAsync();

                var result = await base.DisconnectAsync();
                Status = DeviceStatus.Disconnected;
                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备连接", $"流量泵 {DeviceName} 断开连接异常", ex);
                return false;
            }
        }

        public async Task<bool> InitializeAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    var connected = await ConnectAsync();
                    if (!connected)
                        return false;
                }

                App.AlarmService.Info("设备初始化", $"正在初始化流量泵 {DeviceName}");

                // 确保泵处于停止状态
                await StopAsync();

                // 设置默认参数
                await SetDirectionAsync(false); // 正向
                await SetFlowRateAsync(1.0);     // 默认流量 1 L/min

                // 读取当前状态
                await RefreshStatusAsync();

                App.AlarmService.Info("设备初始化", $"流量泵 {DeviceName} 初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备初始化", $"流量泵 {DeviceName} 初始化异常", ex);
                return false;
            }
        }

        public async Task<bool> ResetAsync()
        {
            try
            {
                App.AlarmService.Info("设备重置", $"正在重置流量泵 {DeviceName}");

                // 停止泵
                await StopAsync();

                // 重置为默认设置
                await SetDirectionAsync(false);
                await SetFlowRateAsync(1.0);

                App.AlarmService.Info("设备重置", $"流量泵 {DeviceName} 重置成功");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备重置", $"流量泵 {DeviceName} 重置异常", ex);
                return false;
            }
        }

        public async Task<DeviceInfo> GetDeviceInfoAsync()
        {
            try
            {
                // 读取设备状态信息
                var speedRegisters = await ReadHoldingRegistersAsync(REAL_SPEED_HIGH_REGISTER, 2);
                var coilStatus = await ReadCoilsAsync(START_STOP_COIL, 4);

                if (speedRegisters == null || coilStatus == null)
                {
                    throw new InvalidOperationException("无法读取设备信息");
                }

                LastCommunicationTime = DateTime.Now;

                var realSpeed = ConvertIntegerRegistersToRpm(speedRegisters[0], speedRegisters[1]);

                return new DeviceInfo
                {
                    DeviceId = DeviceId,
                    DeviceName = DeviceName,
                    Model = Model,
                    Version = "1.0",
                    SerialNumber = $"KC{_slaveAddress:D3}",
                    Properties = new Dictionary<string, object>
                    {
                        ["CurrentFlowRate"] = _currentFlowRate,
                        ["TargetFlowRate"] = _targetFlowRate,
                        ["RealSpeed"] = realSpeed,
                        ["IsRunning"] = coilStatus[0],
                        ["IsReverse"] = coilStatus[2],
                        ["SlaveAddress"] = _slaveAddress,
                        ["MinFlowRate"] = MIN_FLOW_RATE,
                        ["MaxFlowRate"] = MAX_FLOW_RATE,
                        ["Accuracy"] = FLOW_ACCURACY
                    }
                };
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备信息", $"获取流量泵 {DeviceName} 信息异常", ex);
                throw;
            }
        }

        #endregion

        #region IFlowPump 方法实现

        public async Task<bool> SetFlowRateAsync(double flowRate)
        {
            try
            {
                ValidateFlowRate(flowRate);

                // 将流量转换为转速
                var rpm = ConvertFlowRateToRpm(flowRate);
                var (highWord, lowWord) = ConvertRpmToIntegerRegisters(rpm);

                // 写入转速寄存器
                var result1 = await WriteSingleRegisterAsync(SPEED_HIGH_REGISTER, highWord);
                var result2 = await WriteSingleRegisterAsync(SPEED_LOW_REGISTER, lowWord);

                if (result1 && result2)
                {
                    // 保存参数到Flash
                    await WriteSingleCoilAsync(SAVE_PARAMS_COIL, true);

                    _targetFlowRate = flowRate;
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("流量控制", $"流量泵 {DeviceName} 目标流量设置为 {flowRate:F2} L/min (转速: {rpm:F0} RPM)");
                    OnFlowRateChanged(_currentFlowRate, _targetFlowRate);
                }
                else
                {
                    App.AlarmService.Error("流量控制", $"流量泵 {DeviceName} 设置目标流量失败");
                }

                return result1 && result2;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("流量控制", $"流量泵 {DeviceName} 设置目标流量异常", ex);
                return false;
            }
        }

        public async Task<double> GetFlowRateAsync()
        {
            try
            {
                var registers = await ReadHoldingRegistersAsync(REAL_SPEED_HIGH_REGISTER, 2);
                if (registers == null)
                {
                    throw new InvalidOperationException("读取当前流量失败");
                }

                var realSpeed = ConvertIntegerRegistersToRpm(registers[0], registers[1]);
                _currentFlowRate = ConvertRpmToFlowRate(realSpeed);
                LastCommunicationTime = DateTime.Now;

                return _currentFlowRate;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("流量读取", $"流量泵 {DeviceName} 读取当前流量异常", ex);
                throw;
            }
        }

        public async Task<bool> StartAsync()
        {
            try
            {
                var result = await WriteSingleCoilAsync(START_STOP_COIL, true);

                if (result)
                {
                    _isRunning = true;
                    Status = DeviceStatus.Running;
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("泵控制", $"流量泵 {DeviceName} 启动成功");
                }
                else
                {
                    App.AlarmService.Error("泵控制", $"流量泵 {DeviceName} 启动失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("泵控制", $"流量泵 {DeviceName} 启动异常", ex);
                return false;
            }
        }

        public async Task<bool> StopAsync()
        {
            try
            {
                var result = await WriteSingleCoilAsync(START_STOP_COIL, false);

                if (result)
                {
                    _isRunning = false;
                    Status = DeviceStatus.Connected;
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("泵控制", $"流量泵 {DeviceName} 停止成功");
                }
                else
                {
                    App.AlarmService.Error("泵控制", $"流量泵 {DeviceName} 停止失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("泵控制", $"流量泵 {DeviceName} 停止异常", ex);
                return false;
            }
        }

        public async Task<bool> GetRunningStatusAsync()
        {
            try
            {
                var coils = await ReadCoilsAsync(START_STOP_COIL, 1);
                if (coils == null)
                {
                    throw new InvalidOperationException("读取运行状态失败");
                }

                _isRunning = coils[0];
                Status = _isRunning ? DeviceStatus.Running : DeviceStatus.Connected;
                LastCommunicationTime = DateTime.Now;

                return _isRunning;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("状态读取", $"流量泵 {DeviceName} 读取运行状态异常", ex);
                throw;
            }
        }

        public async Task<bool> SetDirectionAsync(bool reverse)
        {
            try
            {
                var result = await WriteSingleCoilAsync(DIRECTION_COIL, reverse);

                if (result)
                {
                    _isReverse = reverse;
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("方向控制", $"流量泵 {DeviceName} 方向设置为 {(reverse ? "反向" : "正向")}");
                }
                else
                {
                    App.AlarmService.Error("方向控制", $"流量泵 {DeviceName} 设置方向失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("方向控制", $"流量泵 {DeviceName} 设置方向异常", ex);
                return false;
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 刷新设备状态
        /// </summary>
        private async Task RefreshStatusAsync()
        {
            try
            {
                // 读取线圈状态
                var coils = await ReadCoilsAsync(START_STOP_COIL, 3);
                if (coils != null)
                {
                    _isRunning = coils[0];
                    _isReverse = coils[2];
                    Status = _isRunning ? DeviceStatus.Running : DeviceStatus.Connected;
                }

                // 读取实时流量
                var oldCurrentFlow = _currentFlowRate;
                await GetFlowRateAsync();

                // 读取目标流量
                var speedRegisters = await ReadHoldingRegistersAsync(SPEED_HIGH_REGISTER, 2);
                if (speedRegisters != null)
                {
                    var targetSpeed = ConvertIntegerRegistersToRpm(speedRegisters[0], speedRegisters[1]);
                    var oldTargetFlow = _targetFlowRate;
                    _targetFlowRate = ConvertRpmToFlowRate(targetSpeed);

                    // 如果流量有变化，触发事件
                    if (Math.Abs(_currentFlowRate - oldCurrentFlow) > 0.01 ||
                        Math.Abs(_targetFlowRate - oldTargetFlow) > 0.01)
                    {
                        OnFlowRateChanged(_currentFlowRate, _targetFlowRate);
                    }
                }

                LastCommunicationTime = DateTime.Now;
            }
            catch (Exception ex)
            {
                App.AlarmService.Warning("状态刷新", $"流量泵 {DeviceName} 状态刷新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证流量范围
        /// </summary>
        private static void ValidateFlowRate(double flowRate)
        {
            if (flowRate < MIN_FLOW_RATE || flowRate > MAX_FLOW_RATE)
            {
                throw new ArgumentOutOfRangeException(nameof(flowRate),
                    $"流量必须在 {MIN_FLOW_RATE} 到 {MAX_FLOW_RATE} L/min 之间，当前值: {flowRate} L/min");
            }
        }

        /// <summary>
        /// 将流量转换为转速
        /// </summary>
        private static double ConvertFlowRateToRpm(double flowRate)
        {
            return flowRate * FLOW_TO_RPM_RATIO;
        }

        /// <summary>
        /// 将转速转换为流量
        /// </summary>
        private static double ConvertRpmToFlowRate(double rpm)
        {
            return rpm / FLOW_TO_RPM_RATIO;
        }

        /// <summary>
        /// 将转速（RPM）转换为两个寄存器值（整型*100）
        /// </summary>
        private static (ushort high, ushort low) ConvertRpmToIntegerRegisters(double rpm)
        {
            // 转速乘以100转换为整型
            var intValue = (uint)(rpm * 100);

            // 分解为高16位和低16位
            var high = (ushort)(intValue >> 16);
            var low = (ushort)(intValue & 0xFFFF);

            return (high, low);
        }

        /// <summary>
        /// 将两个寄存器值（整型*100）转换为转速（RPM）
        /// </summary>
        private static double ConvertIntegerRegistersToRpm(ushort high, ushort low)
        {
            // 合并高16位和低16位
            var intValue = ((uint)high << 16) | low;

            // 除以100得到实际转速
            return intValue / 100.0;
        }

        #endregion

        #region IDisposable 实现

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    // 停止泵
                    StopAsync().Wait(2000);
                }
                catch (Exception ex)
                {
                    App.AlarmService.Error("流量泵驱动", "释放流量泵资源时停止泵失败", ex);
                }
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}
